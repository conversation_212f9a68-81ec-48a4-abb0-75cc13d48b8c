'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { 
  Building2, 
  Plus, 
  Edit, 
  Trash2, 
  MapPin,
  Calendar,
  Users
} from 'lucide-react';
import { farmApi } from '@/services/api';
import { Farm } from '@/types';

export default function FarmsPage() {
  const [farms, setFarms] = useState<Farm[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadFarms();
  }, []);

  const loadFarms = async () => {
    try {
      setLoading(true);
      const farmsData = await farmApi.getFarms();
      setFarms(farmsData);
    } catch (err) {
      setError('Çiftlik verileri yüklenirken hata oluştu');
      console.error('Error loading farms:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteFarm = async (farmId: string) => {
    if (!confirm('Bu çiftliği silmek istediğinizden emin misiniz?')) {
      return;
    }

    try {
      await farmApi.deleteFarm(farmId);
      setFarms(farms.filter(farm => farm.id !== farmId));
    } catch (err) {
      alert('Çiftlik silinirken hata oluştu');
      console.error('Error deleting farm:', err);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Çiftlikler yükleniyor...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <button 
            onClick={loadFarms}
            className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
          >
            Tekrar Dene
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Çiftlik Yönetimi</h1>
          <p className="text-gray-600 mt-1">
            Çiftliklerinizi yönetin ve yeni çiftlik ekleyin
          </p>
        </div>
        <Link
          href="/farms/new"
          className="inline-flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors"
        >
          <Plus className="h-5 w-5" />
          <span>Yeni Çiftlik</span>
        </Link>
      </div>

      {/* Farms Grid */}
      {farms.length === 0 ? (
        <div className="text-center py-12">
          <Building2 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            Henüz çiftlik yok
          </h3>
          <p className="text-gray-600 mb-6">
            İlk çiftliğinizi oluşturarak başlayın
          </p>
          <Link
            href="/farms/new"
            className="inline-flex items-center space-x-2 bg-green-600 text-white px-6 py-3 rounded-md hover:bg-green-700 transition-colors"
          >
            <Plus className="h-5 w-5" />
            <span>İlk Çiftliğimi Oluştur</span>
          </Link>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {farms.map((farm) => (
            <div key={farm.id} className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow">
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <Building2 className="h-8 w-8 text-green-600" />
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">
                        {farm.name}
                      </h3>
                      <div className="flex items-center space-x-1 text-sm text-gray-600">
                        <MapPin className="h-4 w-4" />
                        <span>{farm.location}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Link
                      href={`/farms/${farm.id}/edit`}
                      className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
                    >
                      <Edit className="h-4 w-4" />
                    </Link>
                    <button
                      onClick={() => handleDeleteFarm(farm.id)}
                      className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-md transition-colors"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Toplam Arazi:</span>
                    <span className="font-medium">{farm.total_land_hectares} ha</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Mera Alanı:</span>
                    <span className="font-medium">{farm.pasture_land_hectares} ha</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Ahır Kapasitesi:</span>
                    <span className="font-medium">{farm.barn_capacity} baş</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Kuruluş Tarihi:</span>
                    <span className="font-medium">
                      {new Date(farm.established_date).toLocaleDateString('tr-TR')}
                    </span>
                  </div>
                </div>

                <div className="mt-6 pt-4 border-t border-gray-200">
                  <div className="flex space-x-2">
                    <Link
                      href={`/animals?farm=${farm.id}`}
                      className="flex-1 text-center bg-blue-50 text-blue-700 py-2 px-3 rounded-md text-sm font-medium hover:bg-blue-100 transition-colors"
                    >
                      Hayvanlar
                    </Link>
                    <Link
                      href={`/feeds?farm=${farm.id}`}
                      className="flex-1 text-center bg-yellow-50 text-yellow-700 py-2 px-3 rounded-md text-sm font-medium hover:bg-yellow-100 transition-colors"
                    >
                      Yemler
                    </Link>
                    <Link
                      href={`/simulation?farm=${farm.id}`}
                      className="flex-1 text-center bg-green-50 text-green-700 py-2 px-3 rounded-md text-sm font-medium hover:bg-green-100 transition-colors"
                    >
                      Simülasyon
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
