@import "tailwindcss";

/* <PERSON>zel arka plan tasarımı */
body {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 25%, #f0fdf4 50%, #ecfdf5 75%, #f0f9ff 100%);
  background-attachment: fixed;
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
  color: #1f2937;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Animasyonlu arka plan deseni */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 20% 20%, rgba(34, 197, 94, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(59, 130, 246, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(168, 85, 247, 0.04) 0%, transparent 50%);
  background-size: 600px 600px, 800px 800px, 400px 400px;
  animation: float 25s ease-in-out infinite;
  pointer-events: none;
  z-index: -1;
}

@keyframes float {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  25% { transform: translate(20px, -20px) rotate(90deg); }
  50% { transform: translate(-15px, 15px) rotate(180deg); }
  75% { transform: translate(15px, -10px) rotate(270deg); }
}

/* Fare takip animasyonu */
.mouse-follower {
  position: fixed;
  width: 24px;
  height: 24px;
  background: radial-gradient(circle, rgba(34, 197, 94, 0.2) 0%, rgba(34, 197, 94, 0.1) 50%, transparent 70%);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  transition: transform 0.15s ease-out;
  mix-blend-mode: multiply;
}

/* Hover efektleri */
.hover-glow {
  transition: all 0.3s ease;
}

.hover-glow:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
}

/* Kart animasyonları */
.card-animate {
  animation: slideInUp 0.8s ease-out forwards;
  opacity: 0;
  transform: translateY(40px);
}

.card-animate:nth-child(1) { animation-delay: 0.1s; }
.card-animate:nth-child(2) { animation-delay: 0.2s; }
.card-animate:nth-child(3) { animation-delay: 0.3s; }
.card-animate:nth-child(4) { animation-delay: 0.4s; }
.card-animate:nth-child(5) { animation-delay: 0.5s; }
.card-animate:nth-child(6) { animation-delay: 0.6s; }

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Metin okunabilirliği için arka plan overlay */
.content-overlay {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(12px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Navigation için özel stil */
.nav-glass {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(12px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* Buton hover efektleri */
.btn-primary {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(16, 185, 129, 0.3);
}

/* Pulse animasyonu */
.pulse-slow {
  animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .85;
  }
}

/* Özel scroll bar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(34, 197, 94, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(34, 197, 94, 0.5);
}
