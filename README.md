# Hayvan Yetiştiriciliği Simülasyon Sistemi

Bu proje, s<PERSON><PERSON><PERSON><PERSON> yetiştiriciliği için kapsamlı bir simülasyon ve yönetim sistemi sağlar.

## Özellikler

- 🐄 <PERSON><PERSON> yönetimi ve takibi
- 🌾 Yem ve rasyon planlaması
- 📊 Finansal simülasyon ve analiz
- 📈 Performans raporları
- 🎯 Maliyet optimizasyonu

## Teknoloji Stack

### Backend
- FastAPI
- SQLAlchemy + SQLite
- Pydantic
- NumPy/SciPy

### Frontend
- Next.js 14
- TypeScript
- Tailwind CSS
- Recharts

## Kurulum

### Backend
```bash
cd backend
pip install -r requirements.txt
uvicorn app.main:app --reload
```

### Frontend
```bash
cd frontend
npm install
npm run dev
```

## Kullanım

1. Çiftlik bilgilerini girin
2. Hayvan kayıtlarını ekleyin
3. <PERSON><PERSON> çeşitlerini tanımlayın
4. <PERSON><PERSON>on planlaması yapın
5. Simülasyon çalıştırın
6. Sonuçları analiz edin
