'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { 
  Wheat, 
  Plus, 
  Edit, 
  Trash2, 
  Package,
  DollarSign
} from 'lucide-react';
import { feedApi, farmApi } from '@/services/api';
import { Feed, Farm } from '@/types';

export default function FeedsPage() {
  const searchParams = useSearchParams();
  const farmId = searchParams.get('farm');
  
  const [farms, setFarms] = useState<Farm[]>([]);
  const [selectedFarmId, setSelectedFarmId] = useState<string>(farmId || '');
  const [feeds, setFeeds] = useState<Feed[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadFarms();
  }, []);

  useEffect(() => {
    if (selectedFarmId) {
      loadFeeds(selectedFarmId);
    }
  }, [selectedFarmId]);

  const loadFarms = async () => {
    try {
      const farmsData = await farmApi.getFarms();
      setFarms(farmsData);
      if (!selectedFarmId && farmsData.length > 0) {
        setSelectedFarmId(farmsData[0].id);
      }
    } catch (err) {
      setError('Çiftlik verileri yüklenirken hata oluştu');
      console.error('Error loading farms:', err);
    }
  };

  const loadFeeds = async (farmId: string) => {
    try {
      setLoading(true);
      const feedsData = await feedApi.getFeedsByFarm(farmId);
      setFeeds(feedsData);
    } catch (err) {
      setError('Yem verileri yüklenirken hata oluştu');
      console.error('Error loading feeds:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteFeed = async (feedId: string) => {
    if (!confirm('Bu yemi silmek istediğinizden emin misiniz?')) {
      return;
    }

    try {
      await feedApi.deleteFeed(feedId);
      setFeeds(feeds.filter(feed => feed.id !== feedId));
    } catch (err) {
      alert('Yem silinirken hata oluştu');
      console.error('Error deleting feed:', err);
    }
  };

  const handleAddSampleFeeds = async () => {
    if (!selectedFarmId) return;

    try {
      await feedApi.addSampleFeeds(selectedFarmId);
      loadFeeds(selectedFarmId);
      alert('Örnek yemler başarıyla eklendi!');
    } catch (err) {
      alert('Örnek yemler eklenirken hata oluştu');
      console.error('Error adding sample feeds:', err);
    }
  };

  const getFeedTypeDisplayName = (type: string) => {
    const typeNames: Record<string, string> = {
      'concentrate': 'Konsantre',
      'roughage': 'Kaba Yem',
      'hay': 'Kuru Ot',
      'silage': 'Silaj',
      'pasture': 'Mera',
      'mineral_vitamin': 'Mineral-Vitamin'
    };
    return typeNames[type] || type;
  };

  const getFeedTypeColor = (type: string) => {
    const colors: Record<string, string> = {
      'concentrate': 'bg-blue-100 text-blue-800',
      'roughage': 'bg-green-100 text-green-800',
      'hay': 'bg-yellow-100 text-yellow-800',
      'silage': 'bg-purple-100 text-purple-800',
      'pasture': 'bg-emerald-100 text-emerald-800',
      'mineral_vitamin': 'bg-red-100 text-red-800'
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
  };

  if (farms.length === 0) {
    return (
      <div className="text-center py-12">
        <Wheat className="h-16 w-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-gray-900 mb-2">
          Önce bir çiftlik oluşturun
        </h3>
        <p className="text-gray-600 mb-6">
          Yem eklemek için önce bir çiftlik oluşturmanız gerekiyor
        </p>
        <Link
          href="/farms/new"
          className="btn-primary text-white px-6 py-3 rounded-md"
        >
          Çiftlik Oluştur
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Yem Yönetimi</h1>
          <p className="text-gray-600 mt-1">
            Yem çeşitlerini yönetin ve besin değerlerini takip edin
          </p>
        </div>
        <div className="flex items-center space-x-4">
          {farms.length > 1 && (
            <select
              value={selectedFarmId}
              onChange={(e) => setSelectedFarmId(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 bg-white"
            >
              {farms.map((farm) => (
                <option key={farm.id} value={farm.id}>
                  {farm.name}
                </option>
              ))}
            </select>
          )}
          {selectedFarmId && (
            <>
              <button
                onClick={handleAddSampleFeeds}
                className="inline-flex items-center space-x-2 border border-green-600 text-green-600 px-4 py-2 rounded-md hover:bg-green-50 transition-colors"
              >
                <Package className="h-5 w-5" />
                <span>Örnek Yemler</span>
              </button>
              <Link
                href={`/feeds/new?farm=${selectedFarmId}`}
                className="inline-flex items-center space-x-2 btn-primary text-white px-4 py-2 rounded-md"
              >
                <Plus className="h-5 w-5" />
                <span>Yeni Yem</span>
              </Link>
            </>
          )}
        </div>
      </div>

      {/* Feeds List */}
      {loading ? (
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Yemler yükleniyor...</p>
          </div>
        </div>
      ) : error ? (
        <div className="text-center py-12">
          <p className="text-red-600 mb-4">{error}</p>
          <button 
            onClick={() => selectedFarmId && loadFeeds(selectedFarmId)}
            className="btn-primary text-white px-4 py-2 rounded-md"
          >
            Tekrar Dene
          </button>
        </div>
      ) : feeds.length === 0 ? (
        <div className="text-center py-12">
          <Wheat className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            Henüz yem yok
          </h3>
          <p className="text-gray-600 mb-6">
            İlk yeminizi ekleyerek başlayın veya örnek yemler ekleyin
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            {selectedFarmId && (
              <>
                <button
                  onClick={handleAddSampleFeeds}
                  className="inline-flex items-center space-x-2 border border-green-600 text-green-600 px-6 py-3 rounded-md hover:bg-green-50 transition-colors"
                >
                  <Package className="h-5 w-5" />
                  <span>Örnek Yemler Ekle</span>
                </button>
                <Link
                  href={`/feeds/new?farm=${selectedFarmId}`}
                  className="inline-flex items-center space-x-2 btn-primary text-white px-6 py-3 rounded-md"
                >
                  <Plus className="h-5 w-5" />
                  <span>Yeni Yem Ekle</span>
                </Link>
              </>
            )}
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {feeds.map((feed) => (
            <div key={feed.id} className="content-overlay hover-glow p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <Wheat className="h-8 w-8 text-yellow-600" />
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      {feed.name}
                    </h3>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getFeedTypeColor(feed.feed_type)}`}>
                      {getFeedTypeDisplayName(feed.feed_type)}
                    </span>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <Link
                    href={`/feeds/${feed.id}/edit`}
                    className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
                  >
                    <Edit className="h-4 w-4" />
                  </Link>
                  <button
                    onClick={() => handleDeleteFeed(feed.id)}
                    className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-md transition-colors"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Maliyet:</span>
                  <span className="font-medium flex items-center">
                    <DollarSign className="h-4 w-4 mr-1" />
                    {feed.cost_per_kg.toFixed(2)} ₺/kg
                  </span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Kuru Madde:</span>
                  <span className="font-medium">{feed.dry_matter_percentage}%</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Ham Protein:</span>
                  <span className="font-medium">{feed.crude_protein_percentage}%</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">ME:</span>
                  <span className="font-medium">{feed.metabolizable_energy_mcal_kg} Mcal/kg</span>
                </div>
              </div>

              <div className="mt-4 pt-4 border-t border-gray-200">
                <Link
                  href={`/feeds/${feed.id}`}
                  className="text-center bg-gray-50 text-gray-700 py-2 px-3 rounded-md text-sm font-medium hover:bg-gray-100 transition-colors block"
                >
                  Detayları Görüntüle
                </Link>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
