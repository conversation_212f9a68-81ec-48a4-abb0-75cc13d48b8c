import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import Navigation from "@/components/Navigation";
import MouseFollower from "@/components/MouseFollower";

const inter = Inter({
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Hayvan Yetiştiriciliği Simülasyon Sistemi",
  description: "Sığır yetiştiriciliği için kapsamlı simülasyon ve yönetim sistemi",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="tr">
      <body className={`${inter.className} antialiased`}>
        <MouseFollower />
        <div className="min-h-screen">
          <Navigation />
          <main className="container mx-auto px-4 py-8 relative z-10">
            {children}
          </main>
        </div>
      </body>
    </html>
  );
}
