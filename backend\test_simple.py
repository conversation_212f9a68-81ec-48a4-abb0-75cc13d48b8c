#!/usr/bin/env python3
"""
Basit backend testi
"""

try:
    from fastapi import FastAPI
    print("✓ FastAPI import edildi")
    
    app = FastAPI(title="Test")
    print("✓ FastAPI uygulaması oluşturuldu")
    
    @app.get("/")
    def root():
        return {"message": "Test başarılı"}
    
    print("✓ Endpoint tanımlandı")
    
    # SQLAlchemy test
    from sqlalchemy import create_engine
    print("✓ SQLAlchemy import edildi")
    
    engine = create_engine("sqlite:///test.db")
    print("✓ SQLite engine oluşturuldu")
    
    print("\n🎉 Tüm temel bileşenler çalışıyor!")
    print("Backend hazır, uvicorn ile çalıştırılabilir.")
    
except ImportError as e:
    print(f"❌ Import hatası: {e}")
    print("Gerekli paketler kurulmamış olabilir.")
except Exception as e:
    print(f"❌ Hata: {e}")
