from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from datetime import datetime
import uuid

# In-memory storage
farms_db = []
animals_db = []
feeds_db = []

# FastAPI uygulaması
app = FastAPI(
    title="Hayvan Yetiştiriciliği Simülasyon Sistemi",
    description="<PERSON>ığır yetiştiriciliği için kapsamlı simülasyon ve yönetim sistemi",
    version="1.0.0"
)

# CORS middleware ekle
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # React frontend
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {
        "message": "Hayvan Yetiştiriciliği Simülasyon Sistemi API",
        "version": "1.0.0",
        "docs": "/docs",
        "status": "running"
    }

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

# Basit API endpoint'leri
@app.get("/api/farms/")
async def get_farms():
    return []

@app.post("/api/farms/")
async def create_farm(farm_data: dict):
    return {"id": "test-farm-id", "message": "Çiftlik başarıyla oluşturuldu"}

@app.get("/api/animals/farm/{farm_id}")
async def get_animals(farm_id: str):
    return []

@app.get("/api/animals/farm/{farm_id}/stats")
async def get_animal_stats(farm_id: str):
    return {
        "total_animals": 0,
        "by_gender": {"male": 0, "female": 0},
        "by_status": {},
        "by_breed": {},
        "average_weight": 0,
        "pregnant_count": 0
    }

@app.post("/api/animals/")
async def create_animal(animal_data: dict):
    return {"id": "test-animal-id", "message": "Hayvan başarıyla eklendi"}

@app.get("/api/feeds/farm/{farm_id}")
async def get_feeds(farm_id: str):
    return []

@app.post("/api/feeds/")
async def create_feed(feed_data: dict):
    return {"id": "test-feed-id", "message": "Yem başarıyla eklendi"}

@app.post("/api/feeds/farm/{farm_id}/sample-feeds")
async def add_sample_feeds(farm_id: str):
    return {
        "message": "3 örnek yem başarıyla eklendi",
        "feeds": ["Konsantre Yem", "Kuru Ot", "Mısır Silajı"]
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
