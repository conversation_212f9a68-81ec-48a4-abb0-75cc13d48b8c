// Enum tanımları
export enum CattleBreed {
  ANGUS = "angus",
  HEREFORD = "hereford",
  SIMMENTAL = "simmental",
  CHAROLAIS = "charolais",
  LIMOUSIN = "limousin",
  HOLSTEIN = "holstein",
  BROWN_SWISS = "brown_swiss",
  NATIVE_ANATOLIAN = "native_anatolian",
  CROSSBRED = "crossbred"
}

export enum AnimalStatus {
  CALF = "calf",
  YOUNG = "young",
  BREEDING = "breeding",
  FATTENING = "fattening",
  READY_FOR_SALE = "ready_for_sale",
  SOLD = "sold",
  DEAD = "dead"
}

export enum FeedType {
  CONCENTRATE = "concentrate",
  ROUGHAGE = "roughage",
  HAY = "hay",
  SILAGE = "silage",
  PASTURE = "pasture",
  MINERAL_VITAMIN = "mineral_vitamin"
}

export enum Gender {
  MALE = "male",
  FEMALE = "female"
}

// Interface tanımları
export interface Farm {
  id: string;
  name: string;
  location: string;
  established_date: string;
  total_land_hectares: number;
  pasture_land_hectares: number;
  barn_capacity: number;
  feed_storage_capacity_tons: number;
  silage_capacity_tons: number;
  hay_storage_capacity_tons: number;
  water_storage_capacity_liters: number;
  milking_parlor_capacity?: number;
  quarantine_facility_capacity: number;
  hospital_pen_capacity: number;
  handling_facility_present: boolean;
  scale_capacity_kg: number;
}

export interface Animal {
  id: string;
  farm_id: string;
  breed: CattleBreed;
  birth_date: string;
  gender: Gender;
  current_weight_kg: number;
  body_condition_score: number;
  status: AnimalStatus;
  is_pregnant: boolean;
  age_months: number;
  pregnancy_start_date?: string;
  expected_calving_date?: string;
  dam_id?: string;
  sire_id?: string;
  purchase_price?: number;
  purchase_date?: string;
}

export interface Feed {
  id: string;
  farm_id: string;
  name: string;
  feed_type: FeedType;
  cost_per_kg: number;
  dry_matter_percentage: number;
  crude_protein_percentage: number;
  metabolizable_energy_mcal_kg: number;
  storage_life_days: number;
  moisture_content_percentage: number;
}

export interface FarmCreate {
  name: string;
  location: string;
  total_land_hectares: number;
  pasture_land_hectares: number;
  barn_capacity: number;
  feed_storage_capacity_tons: number;
  silage_capacity_tons: number;
  hay_storage_capacity_tons: number;
  water_storage_capacity_liters: number;
  milking_parlor_capacity?: number;
  quarantine_facility_capacity: number;
  hospital_pen_capacity: number;
  handling_facility_present: boolean;
  scale_capacity_kg: number;
  labor_cost_monthly?: number;
  electricity_cost_monthly?: number;
  water_cost_monthly?: number;
  fuel_cost_monthly?: number;
  insurance_cost_monthly?: number;
  maintenance_cost_monthly?: number;
  veterinary_cost_annual?: number;
  taxation_annual?: number;
  depreciation_annual?: number;
  interest_cost_annual?: number;
  vaccination_cost_per_animal?: number;
  deworming_cost_per_animal?: number;
  hoof_care_cost_per_animal?: number;
  breeding_cost_per_service?: number;
  live_cattle_price_per_kg?: number;
  calf_price_per_head?: number;
  bull_price_per_head?: number;
  cow_price_per_head?: number;
  carcass_price_per_kg?: number;
  manure_price_per_ton?: number;
  hide_price_per_piece?: number;
  conception_rate?: number;
  calving_rate?: number;
  calf_survival_rate?: number;
  weaning_rate?: number;
  mortality_rate_adult?: number;
  mortality_rate_young?: number;
  culling_rate?: number;
  replacement_rate?: number;
  disease_outbreak_probability?: number;
  market_volatility_coefficient?: number;
  weather_risk_probability?: number;
  feed_price_volatility?: number;
  drought_probability?: number;
  flood_probability?: number;
  policy_change_risk?: number;
  discount_rate?: number;
  inflation_rate?: number;
  tax_rate?: number;
}

export interface AnimalCreate {
  farm_id: string;
  breed: CattleBreed;
  birth_date: string;
  gender: Gender;
  current_weight_kg: number;
  body_condition_score: number;
  status: AnimalStatus;
  is_pregnant?: boolean;
  pregnancy_start_date?: string;
  expected_calving_date?: string;
  dam_id?: string;
  sire_id?: string;
  purchase_price?: number;
  purchase_date?: string;
}

export interface AnimalStats {
  total_animals: number;
  by_gender: {
    male: number;
    female: number;
  };
  by_status: Record<string, number>;
  by_breed: Record<string, number>;
  average_weight: number;
  pregnant_count: number;
}

// API Response types
export interface ApiResponse<T> {
  data?: T;
  message?: string;
  error?: string;
}

// Breed characteristics
export interface BreedCharacteristics {
  breed: CattleBreed;
  mature_weight_male_kg: number;
  mature_weight_female_kg: number;
  birth_weight_kg: number;
  weaning_weight_kg: number;
  yearling_weight_kg: number;
  average_daily_gain_kg: number;
  feed_conversion_ratio: number;
  dressing_percentage: number;
  age_at_first_calving_months: number;
  gestation_length_days: number;
  calving_interval_days: number;
  longevity_years: number;
  calving_ease_score: number;
  milk_production_potential_kg: number;
}
