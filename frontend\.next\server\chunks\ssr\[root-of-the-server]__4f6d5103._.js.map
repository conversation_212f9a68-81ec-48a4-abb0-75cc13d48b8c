{"version": 3, "sources": [], "sections": [{"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Vscode/hayvancilik/frontend/src/services/api.ts"], "sourcesContent": ["import axios from 'axios';\nimport { Farm, FarmCreate, Animal, AnimalCreate, AnimalStats, Feed } from '@/types';\n\nconst API_BASE_URL = 'http://localhost:8000/api';\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Farm API\nexport const farmApi = {\n  // Tüm çiftlikleri getir\n  getFarms: async (): Promise<Farm[]> => {\n    const response = await api.get('/farms/');\n    return response.data;\n  },\n\n  // Belirli bir çiftliği getir\n  getFarm: async (farmId: string): Promise<Farm> => {\n    const response = await api.get(`/farms/${farmId}`);\n    return response.data;\n  },\n\n  // Yeni çiftlik oluştur\n  createFarm: async (farmData: FarmCreate): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/farms/', farmData);\n    return response.data;\n  },\n\n  // Çiftlik güncelle\n  updateFarm: async (farmId: string, farmData: FarmCreate): Promise<{ message: string }> => {\n    const response = await api.put(`/farms/${farmId}`, farmData);\n    return response.data;\n  },\n\n  // Çiftlik sil\n  deleteFarm: async (farmId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/farms/${farmId}`);\n    return response.data;\n  },\n};\n\n// Animal API\nexport const animalApi = {\n  // Çiftlikteki hayvanları getir\n  getAnimalsByFarm: async (farmId: string): Promise<Animal[]> => {\n    const response = await api.get(`/animals/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Belirli bir hayvanı getir\n  getAnimal: async (animalId: string): Promise<Animal> => {\n    const response = await api.get(`/animals/${animalId}`);\n    return response.data;\n  },\n\n  // Yeni hayvan ekle\n  createAnimal: async (animalData: AnimalCreate): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/animals/', animalData);\n    return response.data;\n  },\n\n  // Hayvan güncelle\n  updateAnimal: async (animalId: string, animalData: AnimalCreate): Promise<{ message: string }> => {\n    const response = await api.put(`/animals/${animalId}`, animalData);\n    return response.data;\n  },\n\n  // Hayvan sil\n  deleteAnimal: async (animalId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/animals/${animalId}`);\n    return response.data;\n  },\n\n  // Çiftlik hayvan istatistikleri\n  getFarmAnimalStats: async (farmId: string): Promise<AnimalStats> => {\n    const response = await api.get(`/animals/farm/${farmId}/stats`);\n    return response.data;\n  },\n};\n\n// Feed API\nexport const feedApi = {\n  // Çiftlikteki yemleri getir\n  getFeedsByFarm: async (farmId: string): Promise<Feed[]> => {\n    const response = await api.get(`/feeds/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Belirli bir yemi getir\n  getFeed: async (feedId: string): Promise<Feed> => {\n    const response = await api.get(`/feeds/${feedId}`);\n    return response.data;\n  },\n\n  // Yeni yem ekle\n  createFeed: async (feedData: any): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/feeds/', feedData);\n    return response.data;\n  },\n\n  // Yem güncelle\n  updateFeed: async (feedId: string, feedData: any): Promise<{ message: string }> => {\n    const response = await api.put(`/feeds/${feedId}`, feedData);\n    return response.data;\n  },\n\n  // Yem sil\n  deleteFeed: async (feedId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/feeds/${feedId}`);\n    return response.data;\n  },\n\n  // Yem türlerini getir\n  getFeedTypesByFarm: async (farmId: string): Promise<Record<string, any[]>> => {\n    const response = await api.get(`/feeds/farm/${farmId}/types`);\n    return response.data;\n  },\n\n  // Örnek yem verileri ekle\n  addSampleFeeds: async (farmId: string): Promise<{ message: string; feeds: string[] }> => {\n    const response = await api.post(`/feeds/farm/${farmId}/sample-feeds`);\n    return response.data;\n  },\n};\n\n// Health check\nexport const healthApi = {\n  checkHealth: async (): Promise<{ status: string }> => {\n    const response = await api.get('/health', { baseURL: 'http://localhost:8000' });\n    return response.data;\n  },\n};\n\nexport default api;\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAGA,MAAM,eAAe;AAErB,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAGO,MAAM,UAAU;IACrB,wBAAwB;IACxB,UAAU;QACR,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,uBAAuB;IACvB,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,WAAW;QAC3C,OAAO,SAAS,IAAI;IACtB;IAEA,mBAAmB;IACnB,YAAY,OAAO,QAAgB;QACjC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,OAAO,EAAE,QAAQ;QACpD,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,+BAA+B;IAC/B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,QAAQ;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,4BAA4B;IAC5B,WAAW,OAAO;QAChB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,mBAAmB;IACnB,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,aAAa;QAC7C,OAAO,SAAS,IAAI;IACtB;IAEA,kBAAkB;IAClB,cAAc,OAAO,UAAkB;QACrC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU,EAAE;QACvD,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa;IACb,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,gCAAgC;IAChC,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,OAAO,MAAM,CAAC;QAC9D,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,UAAU;IACrB,4BAA4B;IAC5B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,QAAQ;QACtD,OAAO,SAAS,IAAI;IACtB;IAEA,yBAAyB;IACzB,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB;IAChB,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,WAAW;QAC3C,OAAO,SAAS,IAAI;IACtB;IAEA,eAAe;IACf,YAAY,OAAO,QAAgB;QACjC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,UAAU;IACV,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,OAAO,EAAE,QAAQ;QACpD,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,OAAO,MAAM,CAAC;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,YAAY,EAAE,OAAO,aAAa,CAAC;QACpE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,aAAa;QACX,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,WAAW;YAAE,SAAS;QAAwB;QAC7E,OAAO,SAAS,IAAI;IACtB;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Vscode/hayvancilik/frontend/src/app/farms/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { \n  Building2, \n  Plus, \n  Edit, \n  Trash2, \n  MapPin,\n  Calendar,\n  Users\n} from 'lucide-react';\nimport { farmApi } from '@/services/api';\nimport { Farm } from '@/types';\n\nexport default function FarmsPage() {\n  const [farms, setFarms] = useState<Farm[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    loadFarms();\n  }, []);\n\n  const loadFarms = async () => {\n    try {\n      setLoading(true);\n      const farmsData = await farmApi.getFarms();\n      setFarms(farmsData);\n    } catch (err) {\n      setError('Çiftlik verileri yüklenirken hata oluştu');\n      console.error('Error loading farms:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteFarm = async (farmId: string) => {\n    if (!confirm('Bu çiftliği silmek istediğinizden emin misiniz?')) {\n      return;\n    }\n\n    try {\n      await farmApi.deleteFarm(farmId);\n      setFarms(farms.filter(farm => farm.id !== farmId));\n    } catch (err) {\n      alert('Çiftlik silinirken hata oluştu');\n      console.error('Error deleting farm:', err);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-[400px]\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">Çiftlikler yükleniyor...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"flex items-center justify-center min-h-[400px]\">\n        <div className=\"text-center\">\n          <p className=\"text-red-600 mb-4\">{error}</p>\n          <button \n            onClick={loadFarms}\n            className=\"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700\"\n          >\n            Tekrar Dene\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900\">Çiftlik Yönetimi</h1>\n          <p className=\"text-gray-600 mt-1\">\n            Çiftliklerinizi yönetin ve yeni çiftlik ekleyin\n          </p>\n        </div>\n        <Link\n          href=\"/farms/new\"\n          className=\"inline-flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors\"\n        >\n          <Plus className=\"h-5 w-5\" />\n          <span>Yeni Çiftlik</span>\n        </Link>\n      </div>\n\n      {/* Farms Grid */}\n      {farms.length === 0 ? (\n        <div className=\"text-center py-12\">\n          <Building2 className=\"h-16 w-16 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n            Henüz çiftlik yok\n          </h3>\n          <p className=\"text-gray-600 mb-6\">\n            İlk çiftliğinizi oluşturarak başlayın\n          </p>\n          <Link\n            href=\"/farms/new\"\n            className=\"inline-flex items-center space-x-2 bg-green-600 text-white px-6 py-3 rounded-md hover:bg-green-700 transition-colors\"\n          >\n            <Plus className=\"h-5 w-5\" />\n            <span>İlk Çiftliğimi Oluştur</span>\n          </Link>\n        </div>\n      ) : (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {farms.map((farm) => (\n            <div key={farm.id} className=\"bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow\">\n              <div className=\"p-6\">\n                <div className=\"flex items-start justify-between mb-4\">\n                  <div className=\"flex items-center space-x-3\">\n                    <Building2 className=\"h-8 w-8 text-green-600\" />\n                    <div>\n                      <h3 className=\"text-lg font-semibold text-gray-900\">\n                        {farm.name}\n                      </h3>\n                      <div className=\"flex items-center space-x-1 text-sm text-gray-600\">\n                        <MapPin className=\"h-4 w-4\" />\n                        <span>{farm.location}</span>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"flex space-x-2\">\n                    <Link\n                      href={`/farms/${farm.id}/edit`}\n                      className=\"p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors\"\n                    >\n                      <Edit className=\"h-4 w-4\" />\n                    </Link>\n                    <button\n                      onClick={() => handleDeleteFarm(farm.id)}\n                      className=\"p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-md transition-colors\"\n                    >\n                      <Trash2 className=\"h-4 w-4\" />\n                    </button>\n                  </div>\n                </div>\n\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-center justify-between text-sm\">\n                    <span className=\"text-gray-600\">Toplam Arazi:</span>\n                    <span className=\"font-medium\">{farm.total_land_hectares} ha</span>\n                  </div>\n                  <div className=\"flex items-center justify-between text-sm\">\n                    <span className=\"text-gray-600\">Mera Alanı:</span>\n                    <span className=\"font-medium\">{farm.pasture_land_hectares} ha</span>\n                  </div>\n                  <div className=\"flex items-center justify-between text-sm\">\n                    <span className=\"text-gray-600\">Ahır Kapasitesi:</span>\n                    <span className=\"font-medium\">{farm.barn_capacity} baş</span>\n                  </div>\n                  <div className=\"flex items-center justify-between text-sm\">\n                    <span className=\"text-gray-600\">Kuruluş Tarihi:</span>\n                    <span className=\"font-medium\">\n                      {new Date(farm.established_date).toLocaleDateString('tr-TR')}\n                    </span>\n                  </div>\n                </div>\n\n                <div className=\"mt-6 pt-4 border-t border-gray-200\">\n                  <div className=\"flex space-x-2\">\n                    <Link\n                      href={`/animals?farm=${farm.id}`}\n                      className=\"flex-1 text-center bg-blue-50 text-blue-700 py-2 px-3 rounded-md text-sm font-medium hover:bg-blue-100 transition-colors\"\n                    >\n                      Hayvanlar\n                    </Link>\n                    <Link\n                      href={`/feeds?farm=${farm.id}`}\n                      className=\"flex-1 text-center bg-yellow-50 text-yellow-700 py-2 px-3 rounded-md text-sm font-medium hover:bg-yellow-100 transition-colors\"\n                    >\n                      Yemler\n                    </Link>\n                    <Link\n                      href={`/simulation?farm=${farm.id}`}\n                      className=\"flex-1 text-center bg-green-50 text-green-700 py-2 px-3 rounded-md text-sm font-medium hover:bg-green-100 transition-colors\"\n                    >\n                      Simülasyon\n                    </Link>\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AASA;AAbA;;;;;;AAgBe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,YAAY;QAChB,IAAI;YACF,WAAW;YACX,MAAM,YAAY,MAAM,sHAAA,CAAA,UAAO,CAAC,QAAQ;YACxC,SAAS;QACX,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,CAAC,QAAQ,oDAAoD;YAC/D;QACF;QAEA,IAAI;YACF,MAAM,sHAAA,CAAA,UAAO,CAAC,UAAU,CAAC;YACzB,SAAS,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAC5C,EAAE,OAAO,KAAK;YACZ,MAAM;YACN,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAIpC,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;YAKT,MAAM,MAAM,KAAK,kBAChB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;kCACrB,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAGzD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,8OAAC;0CAAK;;;;;;;;;;;;;;;;;qCAIV,8OAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;wBAAkB,WAAU;kCAC3B,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEACX,KAAK,IAAI;;;;;;sEAEZ,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,8OAAC;8EAAM,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;sDAI1B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;oDAC9B,WAAU;8DAEV,cAAA,8OAAC,2MAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,8OAAC;oDACC,SAAS,IAAM,iBAAiB,KAAK,EAAE;oDACvC,WAAU;8DAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8CAKxB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;;wDAAe,KAAK,mBAAmB;wDAAC;;;;;;;;;;;;;sDAE1D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;;wDAAe,KAAK,qBAAqB;wDAAC;;;;;;;;;;;;;sDAE5D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;;wDAAe,KAAK,aAAa;wDAAC;;;;;;;;;;;;;sDAEpD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DACb,IAAI,KAAK,KAAK,gBAAgB,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;8CAK1D,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,CAAC,cAAc,EAAE,KAAK,EAAE,EAAE;gDAChC,WAAU;0DACX;;;;;;0DAGD,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,CAAC,YAAY,EAAE,KAAK,EAAE,EAAE;gDAC9B,WAAU;0DACX;;;;;;0DAGD,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,CAAC,iBAAiB,EAAE,KAAK,EAAE,EAAE;gDACnC,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;uBArEC,KAAK,EAAE;;;;;;;;;;;;;;;;AAiF7B", "debugId": null}}]}