'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeft, Save } from 'lucide-react';
import { animalApi, farmApi } from '@/services/api';
import { AnimalCreate, Farm, CattleBreed, Gender, AnimalStatus } from '@/types';

export default function NewAnimalPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const farmId = searchParams.get('farm');
  
  const [farms, setFarms] = useState<Farm[]>([]);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<AnimalCreate>({
    farm_id: farmId || '',
    breed: CattleBreed.ANGUS,
    birth_date: '',
    gender: Gender.FEMALE,
    current_weight_kg: 0,
    body_condition_score: 3.0,
    status: AnimalStatus.CALF,
    is_pregnant: false,
  });

  useEffect(() => {
    loadFarms();
  }, []);

  const loadFarms = async () => {
    try {
      const farmsData = await farmApi.getFarms();
      setFarms(farmsData);
      if (!farmId && farmsData.length > 0) {
        setFormData(prev => ({ ...prev, farm_id: farmsData[0].id }));
      }
    } catch (err) {
      console.error('Error loading farms:', err);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    const checked = (e.target as HTMLInputElement).checked;
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : 
               type === 'number' ? parseFloat(value) || 0 : 
               value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.farm_id || !formData.birth_date) {
      alert('Çiftlik ve doğum tarihi zorunludur');
      return;
    }

    try {
      setLoading(true);
      await animalApi.createAnimal(formData);
      router.push(`/animals?farm=${formData.farm_id}`);
    } catch (err) {
      alert('Hayvan eklenirken hata oluştu');
      console.error('Error creating animal:', err);
    } finally {
      setLoading(false);
    }
  };

  const breedOptions = [
    { value: CattleBreed.ANGUS, label: 'Angus' },
    { value: CattleBreed.HEREFORD, label: 'Hereford' },
    { value: CattleBreed.SIMMENTAL, label: 'Simmental' },
    { value: CattleBreed.CHAROLAIS, label: 'Charolais' },
    { value: CattleBreed.LIMOUSIN, label: 'Limousin' },
    { value: CattleBreed.HOLSTEIN, label: 'Holstein' },
    { value: CattleBreed.BROWN_SWISS, label: 'Brown Swiss' },
    { value: CattleBreed.NATIVE_ANATOLIAN, label: 'Yerli Anadolu' },
    { value: CattleBreed.CROSSBRED, label: 'Melez' },
  ];

  const statusOptions = [
    { value: AnimalStatus.CALF, label: 'Buzağı' },
    { value: AnimalStatus.YOUNG, label: 'Genç' },
    { value: AnimalStatus.BREEDING, label: 'Damızlık' },
    { value: AnimalStatus.FATTENING, label: 'Besi' },
    { value: AnimalStatus.READY_FOR_SALE, label: 'Satışa Hazır' },
  ];

  if (farms.length === 0) {
    return (
      <div className="text-center py-12">
        <h3 className="text-xl font-semibold text-gray-900 mb-2">
          Önce bir çiftlik oluşturun
        </h3>
        <p className="text-gray-600 mb-6">
          Hayvan eklemek için önce bir çiftlik oluşturmanız gerekiyor
        </p>
        <Link
          href="/farms/new"
          className="btn-primary text-white px-6 py-3 rounded-md"
        >
          Çiftlik Oluştur
        </Link>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link
          href={`/animals${farmId ? `?farm=${farmId}` : ''}`}
          className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
        >
          <ArrowLeft className="h-5 w-5" />
        </Link>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Yeni Hayvan Ekle</h1>
          <p className="text-gray-600 mt-1">
            Hayvan bilgilerini girin ve kaydedin
          </p>
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="content-overlay p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Temel Bilgiler</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Çiftlik *
              </label>
              <select
                name="farm_id"
                value={formData.farm_id}
                onChange={handleInputChange}
                required
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                {farms.map((farm) => (
                  <option key={farm.id} value={farm.id}>
                    {farm.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Irk *
              </label>
              <select
                name="breed"
                value={formData.breed}
                onChange={handleInputChange}
                required
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                {breedOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Cinsiyet *
              </label>
              <select
                name="gender"
                value={formData.gender}
                onChange={handleInputChange}
                required
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                <option value={Gender.FEMALE}>Dişi</option>
                <option value={Gender.MALE}>Erkek</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Doğum Tarihi *
              </label>
              <input
                type="date"
                name="birth_date"
                value={formData.birth_date}
                onChange={handleInputChange}
                required
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Mevcut Ağırlık (kg) *
              </label>
              <input
                type="number"
                name="current_weight_kg"
                value={formData.current_weight_kg}
                onChange={handleInputChange}
                min="0"
                step="0.1"
                required
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Vücut Kondisyon Skoru (1-5)
              </label>
              <input
                type="number"
                name="body_condition_score"
                value={formData.body_condition_score}
                onChange={handleInputChange}
                min="1"
                max="5"
                step="0.1"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Durum
              </label>
              <select
                name="status"
                value={formData.status}
                onChange={handleInputChange}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                {statusOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {formData.gender === Gender.FEMALE && (
              <div className="flex items-center">
                <input
                  type="checkbox"
                  name="is_pregnant"
                  checked={formData.is_pregnant}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                />
                <label className="ml-2 block text-sm text-gray-700">
                  Gebe
                </label>
              </div>
            )}
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end space-x-4">
          <Link
            href={`/animals${farmId ? `?farm=${farmId}` : ''}`}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
          >
            İptal
          </Link>
          <button
            type="submit"
            disabled={loading}
            className="inline-flex items-center space-x-2 btn-primary text-white px-6 py-2 rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <Save className="h-4 w-4" />
            )}
            <span>{loading ? 'Ekleniyor...' : 'Hayvan Ekle'}</span>
          </button>
        </div>
      </form>
    </div>
  );
}
