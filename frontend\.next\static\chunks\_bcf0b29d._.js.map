{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Vscode/hayvancilik/frontend/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport {\n  Building2,\n  Beef,\n  Wheat,\n  TrendingUp,\n  AlertCircle,\n  Plus\n} from 'lucide-react';\n\nexport default function Home() {\n  return (\n    <div className=\"space-y-8\">\n      {/* Header */}\n      <div className=\"text-center py-12\">\n        <Building2 className=\"h-16 w-16 text-green-600 mx-auto mb-4\" />\n        <h1 className=\"text-4xl font-bold text-gray-900 mb-4\">\n          <PERSON><PERSON> Yetiştiriciliği Simülasyon Sistemi\n        </h1>\n        <p className=\"text-xl text-gray-600 mb-8 max-w-2xl mx-auto\">\n          <PERSON><PERSON><PERSON><PERSON><PERSON> yetiştiriciliği için kapsamlı simülasyon ve yönetim sistemi.\n          Çiftliğinizi yönetin, hayvanlarınızı takip edin ve finansal analizler yapın.\n        </p>\n        <Link\n          href=\"/farms\"\n          className=\"inline-flex items-center space-x-2 bg-green-600 text-white px-8 py-4 rounded-lg hover:bg-green-700 transition-colors text-lg font-medium\"\n        >\n          <Plus className=\"h-6 w-6\" />\n          <span>Başlayın</span>\n        </Link>\n      </div>\n\n      {/* Features */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n        <div className=\"bg-white rounded-lg shadow-lg p-6 text-center\">\n          <Building2 className=\"h-12 w-12 text-blue-500 mx-auto mb-4\" />\n          <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n            Çiftlik Yönetimi\n          </h3>\n          <p className=\"text-gray-600\">\n            Çiftlik altyapısını, kapasiteyi ve işletme maliyetlerini yönetin.\n          </p>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-lg p-6 text-center\">\n          <Beef className=\"h-12 w-12 text-red-500 mx-auto mb-4\" />\n          <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n            Hayvan Takibi\n          </h3>\n          <p className=\"text-gray-600\">\n            Hayvanlarınızın sağlık durumunu, performansını ve üreme kayıtlarını takip edin.\n          </p>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-lg p-6 text-center\">\n          <Wheat className=\"h-12 w-12 text-yellow-500 mx-auto mb-4\" />\n          <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n            Yem Planlaması\n          </h3>\n          <p className=\"text-gray-600\">\n            Optimal rasyon hesaplamaları yapın ve yem maliyetlerini optimize edin.\n          </p>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-lg p-6 text-center\">\n          <TrendingUp className=\"h-12 w-12 text-green-500 mx-auto mb-4\" />\n          <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n            Finansal Simülasyon\n          </h3>\n          <p className=\"text-gray-600\">\n            Monte Carlo simülasyonu ile finansal projeksiyonlar ve risk analizi yapın.\n          </p>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-lg p-6 text-center\">\n          <div className=\"h-12 w-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n            <span className=\"text-purple-600 text-2xl\">📊</span>\n          </div>\n          <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n            Detaylı Raporlar\n          </h3>\n          <p className=\"text-gray-600\">\n            Performans raporları, karlılık analizi ve karar destek araçları.\n          </p>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-lg p-6 text-center\">\n          <div className=\"h-12 w-12 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n            <span className=\"text-indigo-600 text-2xl\">🔬</span>\n          </div>\n          <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n            Bilimsel Hesaplamalar\n          </h3>\n          <p className=\"text-gray-600\">\n            NRC standartlarına uygun besin değeri hesaplamaları ve optimizasyon.\n          </p>\n        </div>\n      </div>\n\n      {/* CTA Section */}\n      <div className=\"bg-green-50 rounded-lg p-8 text-center\">\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">\n          Hemen Başlayın\n        </h2>\n        <p className=\"text-gray-600 mb-6\">\n          Çiftliğinizi kaydedin ve hayvan yetiştiriciliğinizi optimize etmeye başlayın.\n        </p>\n        <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n          <Link\n            href=\"/farms\"\n            className=\"bg-green-600 text-white px-6 py-3 rounded-md hover:bg-green-700 transition-colors\"\n          >\n            Çiftlik Oluştur\n          </Link>\n          <Link\n            href=\"/simulation\"\n            className=\"border border-green-600 text-green-600 px-6 py-3 rounded-md hover:bg-green-50 transition-colors\"\n          >\n            Demo Simülasyon\n          </Link>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;AAae,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;kCACrB,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,6LAAC;wBAAE,WAAU;kCAA+C;;;;;;kCAI5D,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;;0BAKV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CACrB,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAGzD,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAK/B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAGzD,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAK/B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAGzD,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAK/B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;0CACtB,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAGzD,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAK/B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAA2B;;;;;;;;;;;0CAE7C,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAGzD,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAK/B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAA2B;;;;;;;;;;;0CAE7C,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAGzD,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;0BAOjC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AAOX;KAlHwB", "debugId": null}}, {"offset": {"line": 369, "column": 0}, "map": {"version": 3, "file": "trending-up.js", "sources": ["file:///C:/Users/<USER>/Documents/Vscode/hayvancilik/frontend/node_modules/lucide-react/src/icons/trending-up.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 7h6v6', key: 'box55l' }],\n  ['path', { d: 'm22 7-8.5 8.5-5-5L2 17', key: '1t1m79' }],\n];\n\n/**\n * @component @name TrendingUp\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgN2g2djYiIC8+CiAgPHBhdGggZD0ibTIyIDctOC41IDguNS01LTVMMiAxNyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/trending-up\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TrendingUp = createLucideIcon('trending-up', __iconNode);\n\nexport default TrendingUp;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA0B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACzD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAiB,AAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 415, "column": 0}, "map": {"version": 3, "file": "plus.js", "sources": ["file:///C:/Users/<USER>/Documents/Vscode/hayvancilik/frontend/node_modules/lucide-react/src/icons/plus.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M5 12h14', key: '1ays0h' }],\n  ['path', { d: 'M12 5v14', key: 's699le' }],\n];\n\n/**\n * @component @name Plus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KICA8cGF0aCBkPSJNMTIgNXYxNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/plus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Plus = createLucideIcon('plus', __iconNode);\n\nexport default Plus;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}