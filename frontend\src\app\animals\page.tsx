'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { 
  Beef, 
  Plus, 
  Edit, 
  Trash2, 
  Calendar,
  Weight,
  Heart,
  Baby
} from 'lucide-react';
import { animalApi, farmApi } from '@/services/api';
import { Animal, Farm, AnimalStats } from '@/types';

export default function AnimalsPage() {
  const searchParams = useSearchParams();
  const farmId = searchParams.get('farm');
  
  const [farms, setFarms] = useState<Farm[]>([]);
  const [selectedFarmId, setSelectedFarmId] = useState<string>(farmId || '');
  const [animals, setAnimals] = useState<Animal[]>([]);
  const [stats, setStats] = useState<AnimalStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadFarms();
  }, []);

  useEffect(() => {
    if (selectedFarmId) {
      loadAnimals(selectedFarmId);
      loadStats(selectedFarmId);
    }
  }, [selectedFarmId]);

  const loadFarms = async () => {
    try {
      const farmsData = await farmApi.getFarms();
      setFarms(farmsData);
      if (!selectedFarmId && farmsData.length > 0) {
        setSelectedFarmId(farmsData[0].id);
      }
    } catch (err) {
      setError('Çiftlik verileri yüklenirken hata oluştu');
      console.error('Error loading farms:', err);
    }
  };

  const loadAnimals = async (farmId: string) => {
    try {
      setLoading(true);
      const animalsData = await animalApi.getAnimalsByFarm(farmId);
      setAnimals(animalsData);
    } catch (err) {
      setError('Hayvan verileri yüklenirken hata oluştu');
      console.error('Error loading animals:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async (farmId: string) => {
    try {
      const statsData = await animalApi.getFarmAnimalStats(farmId);
      setStats(statsData);
    } catch (err) {
      console.error('Error loading stats:', err);
    }
  };

  const handleDeleteAnimal = async (animalId: string) => {
    if (!confirm('Bu hayvanı silmek istediğinizden emin misiniz?')) {
      return;
    }

    try {
      await animalApi.deleteAnimal(animalId);
      setAnimals(animals.filter(animal => animal.id !== animalId));
      if (selectedFarmId) {
        loadStats(selectedFarmId);
      }
    } catch (err) {
      alert('Hayvan silinirken hata oluştu');
      console.error('Error deleting animal:', err);
    }
  };

  const getBreedDisplayName = (breed: string) => {
    const breedNames: Record<string, string> = {
      'angus': 'Angus',
      'hereford': 'Hereford',
      'simmental': 'Simmental',
      'charolais': 'Charolais',
      'limousin': 'Limousin',
      'holstein': 'Holstein',
      'brown_swiss': 'Brown Swiss',
      'native_anatolian': 'Yerli Anadolu',
      'crossbred': 'Melez'
    };
    return breedNames[breed] || breed;
  };

  const getStatusDisplayName = (status: string) => {
    const statusNames: Record<string, string> = {
      'calf': 'Buzağı',
      'young': 'Genç',
      'breeding': 'Damızlık',
      'fattening': 'Besi',
      'ready_for_sale': 'Satışa Hazır',
      'sold': 'Satıldı',
      'dead': 'Öldü'
    };
    return statusNames[status] || status;
  };

  const getStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      'calf': 'bg-blue-100 text-blue-800',
      'young': 'bg-green-100 text-green-800',
      'breeding': 'bg-purple-100 text-purple-800',
      'fattening': 'bg-yellow-100 text-yellow-800',
      'ready_for_sale': 'bg-orange-100 text-orange-800',
      'sold': 'bg-gray-100 text-gray-800',
      'dead': 'bg-red-100 text-red-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  if (farms.length === 0) {
    return (
      <div className="text-center py-12">
        <Beef className="h-16 w-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-gray-900 mb-2">
          Önce bir çiftlik oluşturun
        </h3>
        <p className="text-gray-600 mb-6">
          Hayvan eklemek için önce bir çiftlik oluşturmanız gerekiyor
        </p>
        <Link
          href="/farms/new"
          className="inline-flex items-center space-x-2 bg-green-600 text-white px-6 py-3 rounded-md hover:bg-green-700 transition-colors"
        >
          <Plus className="h-5 w-5" />
          <span>Çiftlik Oluştur</span>
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Hayvan Yönetimi</h1>
          <p className="text-gray-600 mt-1">
            Hayvanlarınızı yönetin ve takip edin
          </p>
        </div>
        <div className="flex items-center space-x-4">
          {farms.length > 1 && (
            <select
              value={selectedFarmId}
              onChange={(e) => setSelectedFarmId(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 bg-white"
            >
              {farms.map((farm) => (
                <option key={farm.id} value={farm.id}>
                  {farm.name}
                </option>
              ))}
            </select>
          )}
          {selectedFarmId && (
            <Link
              href={`/animals/new?farm=${selectedFarmId}`}
              className="inline-flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors"
            >
              <Plus className="h-5 w-5" />
              <span>Yeni Hayvan</span>
            </Link>
          )}
        </div>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Toplam Hayvan</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.total_animals}
                </p>
              </div>
              <Beef className="h-8 w-8 text-blue-500" />
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Erkek</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.by_gender.male}
                </p>
              </div>
              <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 font-bold">♂</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Dişi</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.by_gender.female}
                </p>
              </div>
              <div className="h-8 w-8 bg-pink-100 rounded-full flex items-center justify-center">
                <span className="text-pink-600 font-bold">♀</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Ortalama Ağırlık</p>
                <p className="text-2xl font-bold text-gray-900">
                  {Math.round(stats.average_weight)} kg
                </p>
              </div>
              <Weight className="h-8 w-8 text-green-500" />
            </div>
          </div>
        </div>
      )}

      {/* Animals List */}
      {loading ? (
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Hayvanlar yükleniyor...</p>
          </div>
        </div>
      ) : error ? (
        <div className="text-center py-12">
          <p className="text-red-600 mb-4">{error}</p>
          <button 
            onClick={() => selectedFarmId && loadAnimals(selectedFarmId)}
            className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
          >
            Tekrar Dene
          </button>
        </div>
      ) : animals.length === 0 ? (
        <div className="text-center py-12">
          <Beef className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            Henüz hayvan yok
          </h3>
          <p className="text-gray-600 mb-6">
            İlk hayvanınızı ekleyerek başlayın
          </p>
          {selectedFarmId && (
            <Link
              href={`/animals/new?farm=${selectedFarmId}`}
              className="inline-flex items-center space-x-2 bg-green-600 text-white px-6 py-3 rounded-md hover:bg-green-700 transition-colors"
            >
              <Plus className="h-5 w-5" />
              <span>İlk Hayvanımı Ekle</span>
            </Link>
          )}
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Hayvan
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Irk
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Yaş
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Ağırlık
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Durum
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Özel Durum
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    İşlemler
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {animals.map((animal) => (
                  <tr key={animal.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className={`h-10 w-10 rounded-full flex items-center justify-center ${
                            animal.gender === 'male' ? 'bg-blue-100' : 'bg-pink-100'
                          }`}>
                            <span className={`font-bold ${
                              animal.gender === 'male' ? 'text-blue-600' : 'text-pink-600'
                            }`}>
                              {animal.gender === 'male' ? '♂' : '♀'}
                            </span>
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            #{animal.id.slice(-8)}
                          </div>
                          <div className="text-sm text-gray-500">
                            {animal.gender === 'male' ? 'Erkek' : 'Dişi'}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {getBreedDisplayName(animal.breed)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {animal.age_months} ay
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {animal.current_weight_kg} kg
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(animal.status)}`}>
                        {getStatusDisplayName(animal.status)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {animal.is_pregnant && (
                        <span className="inline-flex items-center space-x-1 text-green-600">
                          <Baby className="h-4 w-4" />
                          <span>Gebe</span>
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <Link
                          href={`/animals/${animal.id}/edit`}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          <Edit className="h-4 w-4" />
                        </Link>
                        <button
                          onClick={() => handleDeleteAnimal(animal.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
}
