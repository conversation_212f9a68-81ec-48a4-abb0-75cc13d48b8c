'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  Building2,
  Beef,
  Wheat,
  TrendingUp,
  AlertCircle,
  Plus
} from 'lucide-react';

export default function Home() {
  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center py-12">
        <Building2 className="h-16 w-16 text-green-600 mx-auto mb-4" />
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          <PERSON><PERSON> Yetiştiriciliği Simülasyon Sistemi
        </h1>
        <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
          <PERSON><PERSON><PERSON><PERSON><PERSON> yetiştiriciliği için kapsamlı simülasyon ve yönetim sistemi.
          Çiftliğinizi yönetin, hayvanlarınızı takip edin ve finansal analizler yapın.
        </p>
        <Link
          href="/farms"
          className="inline-flex items-center space-x-2 btn-primary text-white px-8 py-4 rounded-lg text-lg font-medium"
        >
          <Plus className="h-6 w-6" />
          <span>Başlayın</span>
        </Link>
      </div>

      {/* Features */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div className="content-overlay hover-glow card-animate p-6 text-center">
          <Building2 className="h-12 w-12 text-blue-500 mx-auto mb-4 pulse-slow" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            Çiftlik Yönetimi
          </h3>
          <p className="text-gray-600">
            Çiftlik altyapısını, kapasiteyi ve işletme maliyetlerini yönetin.
          </p>
        </div>

        <div className="content-overlay hover-glow card-animate p-6 text-center">
          <Beef className="h-12 w-12 text-red-500 mx-auto mb-4 pulse-slow" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            Hayvan Takibi
          </h3>
          <p className="text-gray-600">
            Hayvanlarınızın sağlık durumunu, performansını ve üreme kayıtlarını takip edin.
          </p>
        </div>

        <div className="content-overlay hover-glow card-animate p-6 text-center">
          <Wheat className="h-12 w-12 text-yellow-500 mx-auto mb-4 pulse-slow" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            Yem Planlaması
          </h3>
          <p className="text-gray-600">
            Optimal rasyon hesaplamaları yapın ve yem maliyetlerini optimize edin.
          </p>
        </div>

        <div className="content-overlay hover-glow card-animate p-6 text-center">
          <TrendingUp className="h-12 w-12 text-green-500 mx-auto mb-4 pulse-slow" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            Finansal Simülasyon
          </h3>
          <p className="text-gray-600">
            Monte Carlo simülasyonu ile finansal projeksiyonlar ve risk analizi yapın.
          </p>
        </div>

        <div className="content-overlay hover-glow card-animate p-6 text-center">
          <div className="h-12 w-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4 pulse-slow">
            <span className="text-purple-600 text-2xl">📊</span>
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            Detaylı Raporlar
          </h3>
          <p className="text-gray-600">
            Performans raporları, karlılık analizi ve karar destek araçları.
          </p>
        </div>

        <div className="content-overlay hover-glow card-animate p-6 text-center">
          <div className="h-12 w-12 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4 pulse-slow">
            <span className="text-indigo-600 text-2xl">🔬</span>
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            Bilimsel Hesaplamalar
          </h3>
          <p className="text-gray-600">
            NRC standartlarına uygun besin değeri hesaplamaları ve optimizasyon.
          </p>
        </div>
      </div>

      {/* CTA Section */}
      <div className="content-overlay hover-glow p-8 text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          Hemen Başlayın
        </h2>
        <p className="text-gray-600 mb-6">
          Çiftliğinizi kaydedin ve hayvan yetiştiriciliğinizi optimize etmeye başlayın.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            href="/farms"
            className="btn-primary text-white px-6 py-3 rounded-md transition-all"
          >
            Çiftlik Oluştur
          </Link>
          <Link
            href="/simulation"
            className="border border-green-600 text-green-600 px-6 py-3 rounded-md hover:bg-green-50 transition-colors hover-glow"
          >
            Demo Simülasyon
          </Link>
        </div>
      </div>
    </div>
  );
}
