'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Home,
  Building2,
  Beef,
  Wheat,
  Calculator,
  BarChart3,
  FileText
} from 'lucide-react';

const Navigation = () => {
  const pathname = usePathname();

  const navItems = [
    {
      name: '<PERSON>',
      href: '/',
      icon: Home,
    },
    {
      name: 'Çiftlik Yönetimi',
      href: '/farms',
      icon: Building2,
    },
    {
      name: '<PERSON><PERSON>',
      href: '/animals',
      icon: Beef,
    },
    {
      name: '<PERSON><PERSON>',
      href: '/feeds',
      icon: Wheat,
    },
    {
      name: '<PERSON><PERSON><PERSON>',
      href: '/ration',
      icon: Calculator,
    },
    {
      name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      href: '/simulation',
      icon: BarChart3,
    },
    {
      name: '<PERSON><PERSON><PERSON>',
      href: '/reports',
      icon: FileText,
    },
  ];

  return (
    <nav className="nav-glass shadow-lg border-b border-white/20">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center space-x-2">
            <Beef className="h-8 w-8 text-green-600" />
            <span className="text-xl font-bold text-gray-800">
              Hayvan Yetiştiriciliği
            </span>
          </div>

          {/* Navigation Links */}
          <div className="hidden md:flex items-center space-x-1">
            {navItems.map((item) => {
              const Icon = item.icon;
              const isActive = pathname === item.href;

              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    isActive
                      ? 'bg-green-100 text-green-700'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{item.name}</span>
                </Link>
              );
            })}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              type="button"
              className="text-gray-600 hover:text-gray-900 focus:outline-none focus:text-gray-900"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        <div className="md:hidden">
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
            {navItems.map((item) => {
              const Icon = item.icon;
              const isActive = pathname === item.href;

              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`flex items-center space-x-2 px-3 py-2 rounded-md text-base font-medium transition-colors ${
                    isActive
                      ? 'bg-green-100 text-green-700'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                >
                  <Icon className="h-5 w-5" />
                  <span>{item.name}</span>
                </Link>
              );
            })}
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navigation;
