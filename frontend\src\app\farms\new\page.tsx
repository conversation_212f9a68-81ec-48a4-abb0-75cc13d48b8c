'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeft, Save } from 'lucide-react';
import { farmApi } from '@/services/api';
import { FarmCreate } from '@/types';

export default function NewFarmPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<FarmCreate>({
    name: '',
    location: '',
    total_land_hectares: 0,
    pasture_land_hectares: 0,
    barn_capacity: 0,
    feed_storage_capacity_tons: 0,
    silage_capacity_tons: 0,
    hay_storage_capacity_tons: 0,
    water_storage_capacity_liters: 0,
    quarantine_facility_capacity: 0,
    hospital_pen_capacity: 0,
    handling_facility_present: false,
    scale_capacity_kg: 0,
    // Varsayılan değerler
    labor_cost_monthly: 0,
    electricity_cost_monthly: 0,
    water_cost_monthly: 0,
    fuel_cost_monthly: 0,
    insurance_cost_monthly: 0,
    maintenance_cost_monthly: 0,
    veterinary_cost_annual: 0,
    taxation_annual: 0,
    depreciation_annual: 0,
    interest_cost_annual: 0,
    vaccination_cost_per_animal: 0,
    deworming_cost_per_animal: 0,
    hoof_care_cost_per_animal: 0,
    breeding_cost_per_service: 0,
    live_cattle_price_per_kg: 0,
    calf_price_per_head: 0,
    bull_price_per_head: 0,
    cow_price_per_head: 0,
    carcass_price_per_kg: 0,
    manure_price_per_ton: 0,
    hide_price_per_piece: 0,
    conception_rate: 0.85,
    calving_rate: 0.90,
    calf_survival_rate: 0.95,
    weaning_rate: 0.90,
    mortality_rate_adult: 0.02,
    mortality_rate_young: 0.05,
    culling_rate: 0.15,
    replacement_rate: 0.20,
    disease_outbreak_probability: 0.1,
    market_volatility_coefficient: 0.15,
    weather_risk_probability: 0.2,
    feed_price_volatility: 0.1,
    drought_probability: 0.1,
    flood_probability: 0.05,
    policy_change_risk: 0.1,
    discount_rate: 0.08,
    inflation_rate: 0.05,
    tax_rate: 0.20
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : type === 'number' ? parseFloat(value) || 0 : value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.location) {
      alert('Çiftlik adı ve konum zorunludur');
      return;
    }

    try {
      setLoading(true);
      await farmApi.createFarm(formData);
      router.push('/farms');
    } catch (err) {
      alert('Çiftlik oluşturulurken hata oluştu');
      console.error('Error creating farm:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link
          href="/farms"
          className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
        >
          <ArrowLeft className="h-5 w-5" />
        </Link>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Yeni Çiftlik Oluştur</h1>
          <p className="text-gray-600 mt-1">
            Çiftlik bilgilerini girin ve yeni çiftliğinizi oluşturun
          </p>
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Temel Bilgiler */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Temel Bilgiler</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Çiftlik Adı *
              </label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                placeholder="Örn: Yeşil Vadi Çiftliği"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Konum *
              </label>
              <input
                type="text"
                name="location"
                value={formData.location}
                onChange={handleInputChange}
                required
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                placeholder="Örn: Ankara, Türkiye"
              />
            </div>
          </div>
        </div>

        {/* Altyapı Bilgileri */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Altyapı Bilgileri</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Toplam Arazi (ha)
              </label>
              <input
                type="number"
                name="total_land_hectares"
                value={formData.total_land_hectares}
                onChange={handleInputChange}
                min="0"
                step="0.1"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Mera Alanı (ha)
              </label>
              <input
                type="number"
                name="pasture_land_hectares"
                value={formData.pasture_land_hectares}
                onChange={handleInputChange}
                min="0"
                step="0.1"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Ahır Kapasitesi (baş)
              </label>
              <input
                type="number"
                name="barn_capacity"
                value={formData.barn_capacity}
                onChange={handleInputChange}
                min="0"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Yem Depo Kapasitesi (ton)
              </label>
              <input
                type="number"
                name="feed_storage_capacity_tons"
                value={formData.feed_storage_capacity_tons}
                onChange={handleInputChange}
                min="0"
                step="0.1"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Silaj Kapasitesi (ton)
              </label>
              <input
                type="number"
                name="silage_capacity_tons"
                value={formData.silage_capacity_tons}
                onChange={handleInputChange}
                min="0"
                step="0.1"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Kuru Ot Kapasitesi (ton)
              </label>
              <input
                type="number"
                name="hay_storage_capacity_tons"
                value={formData.hay_storage_capacity_tons}
                onChange={handleInputChange}
                min="0"
                step="0.1"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Su Depo Kapasitesi (litre)
              </label>
              <input
                type="number"
                name="water_storage_capacity_liters"
                value={formData.water_storage_capacity_liters}
                onChange={handleInputChange}
                min="0"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Karantina Kapasitesi (baş)
              </label>
              <input
                type="number"
                name="quarantine_facility_capacity"
                value={formData.quarantine_facility_capacity}
                onChange={handleInputChange}
                min="0"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Hastane Kapasitesi (baş)
              </label>
              <input
                type="number"
                name="hospital_pen_capacity"
                value={formData.hospital_pen_capacity}
                onChange={handleInputChange}
                min="0"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Terazi Kapasitesi (kg)
              </label>
              <input
                type="number"
                name="scale_capacity_kg"
                value={formData.scale_capacity_kg}
                onChange={handleInputChange}
                min="0"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                name="handling_facility_present"
                checked={formData.handling_facility_present}
                onChange={handleInputChange}
                className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
              />
              <label className="ml-2 block text-sm text-gray-700">
                Hayvan Yönetim Tesisi Mevcut
              </label>
            </div>
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end space-x-4">
          <Link
            href="/farms"
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
          >
            İptal
          </Link>
          <button
            type="submit"
            disabled={loading}
            className="inline-flex items-center space-x-2 bg-green-600 text-white px-6 py-2 rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <Save className="h-4 w-4" />
            )}
            <span>{loading ? 'Oluşturuluyor...' : 'Çiftlik Oluştur'}</span>
          </button>
        </div>
      </form>
    </div>
  );
}
